import { When, Then, Given } from '@wdio/cucumber-framework';
import Registration from '../../pages/sensa/sensa-registration.page.ts';
import logger from '../../support/utils/logger.util.ts';
import sensaRegistrationPageObject from '../../page-object/sensa/sensa-registration.pageObject.ts';


Given(/^The user is on the login page for (.*) with login (.*)$/, async function (brand: string, url: string) {
    await Registration.navigateToUrl(url);
    await expect (sensaRegistrationPageObject.lblsignIn_sensa).toBeDisplayed();
    logger.info("Entered URl Successfully");
});

Then(/^The user clicks on the Join Now$/, async function () {
    await Registration.clickonjoinNowButton();
});

When(/^The user enters the valid details added in verated data and click continue button$/, async function () {
    await Registration.enterDetailsintellUsaboutpage();
});

Then(/^The user enters user details in the Account Set up page with Password (.*) Confirm Password (.*) Security Question (.*) Security answer (.*) in the Account setup page$/, async function (password: string, Cpassword: string, securityquestion: string, securityanswer: string) {
    await Registration.enterDetailsinaccountSetupPage(password, Cpassword, securityquestion, securityanswer);
});

When(/^The user validates and enter the phone number (.*) in the popup$/, async function (mobileNumber: string) {
    await Registration.enterMobileNumberinverifyIdentityPage(mobileNumber);
});

Then(/^The user enters valid details Verify your identity page$/, async function () {
    await Registration.enterSSNinverifyssnPage();
});

Then(/^The user validate signup page page$/, async function () {
    await Registration.navigatetoSignupPageandClickOnNoThanksLink();
});

Then(/^The user validate congratulation message page$/, async function () {
    await Registration.navigateTocongratulationsPageandClickOnTakemeToSiteLink();
});

Then(/^The user should be able to login to the application successfully$/, async function () {
    await Registration.NavigatehomepageandverifySensaLogo();
});

Then(/^The user validates that successfully logged out$/, async function () {
    await Registration.successfullLogoutfromSensasite();
});

When(/^The user enters email as (.*)$/, async function (email: string) {
    await Registration.entersEMailinemailField(email);
});

Then(/^The user navigates to signin page and validates error message (.*)$/, async function (mssg: string) {
    await Registration.verifyRegistrationerrorinLoginPage(mssg);
});

Then(/^The user navigates to signin page and validates message (.*)$/, async function (mssg: string) {
    await Registration.verifyifaccountexistandvalidatemessage(mssg);
});

Then(/^The user validates Tell us Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.tellUsaboutpageValidationinRegistration(filepath, sheetname, scenarioname);
});

Then(/^The user validates Account Setup Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.accountsetupPagevalidation(filepath, sheetname, scenarioname);
});
Then(/^The user validates Verify your identity page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.verifyssnpageValidation(filepath, sheetname, scenarioname);
});
Then(/^The user validates Signup with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.signuppageValidation(filepath, sheetname, scenarioname);
});
Then(/^The user validates Errors in Tell us Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.tellUsaboutpageAllErrorsvalidation(filepath, sheetname, scenarioname);
});
Then(/^The user validates Errors in Account Setup Page with filepath (.*) sheet name (.*) and scenario name (.*)$/, async function (filepath: string, sheetname: string, scenarioname: string) {
    await Registration.accountsetuperrorvalidation(filepath, sheetname, scenarioname);
});
Then(/^The user enters Password as (.*) and confirm Password (.*) and validates error message (.*)$/, async function (password: string, confirmpassword: string, errormssg: string) {
    await Registration.passwordFieldErrorValidation(password, confirmpassword, errormssg);
});

When(/^The user enters first Name as (.*) Last Name as (.*) street address as (.*) Zipcode as (.*) City as (.*) and DOB as (.*)$/, async function (firstname: string,lastname: string, address: string, zipcode: string,city:string,dob:string) {
    await Registration.tellUsaboutpageDetailsindcamflow(firstname, lastname,address,zipcode,city,dob);
});

